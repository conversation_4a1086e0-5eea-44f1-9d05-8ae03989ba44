# Bootstrap 4 to Bootstrap 5 Migration

This document tracks the migration process from Bootstrap 4 to Bootstrap 5 for the Class project.

## Current Bootstrap Version
- Bootstrap 4.6.2

## Migration Strategy
We're taking a page-by-page approach to migrate from Bootstrap 4 to Bootstrap 5:
1. Create a separate layout file for Bootstrap 5 pages (`app-bs5.blade.php`)
2. Migrate pages one by one by changing the layout they extend
3. Update component markup to use Bootstrap 5 classes and attributes
4. Test each page thoroughly before moving to the next
5. Once all pages are migrated, remove the Bootstrap 4 layout

### Component Migration Approach
For Vue components, we follow these guidelines:
1. For reusable components that are used across multiple pages (like Panel, Modal, AreYouSure), create BS5 versions in a bs5 subdirectory
2. For page-specific components, update them in place to use Bootstrap 5 classes and attributes
3. When updating a component, make sure to:
   - Update all data attributes (data-toggle → data-bs-toggle, data-target → data-bs-target)
   - Update form classes (form-group → mb-3, add form-label to labels)
   - Update import paths to use BS5 versions of reusable components
   - Test the component thoroughly after migration

## Major Changes in Bootstrap 5

### Breaking Changes
- jQuery dependency removed
- Data attributes renamed with bs prefix (data-toggle → data-bs-toggle)
- Form controls redesigned with simpler class structure
- Close button markup changed
- Select inputs use .form-select instead of .form-control
- Popper.js updated to @popperjs/core
- Dropped support for IE10 and IE11

### New Features
- Enhanced grid system
- RTL support
- New utilities API
- New accordion component
- New offcanvas component
- Improved forms and validation

## Migration Progress

### Completed
- [x] Created Bootstrap 5 test pages using CDN
- [x] Created Bootstrap 5 component comparison page
- [x] Created Bootstrap 5 migration guide
- [x] Created migration log (this document)
- [x] Created a separate layout file for Bootstrap 5 pages (`app-bs5.blade.php`)
- [x] Created an example page using the Bootstrap 5 layout
- [x] Added routes for all test and example pages
- [x] Created custom CSS for Bootstrap 5 with Class colors
- [x] Removed jQuery dependency from Bootstrap 5 layout
- [x] Created Bootstrap 5 versions of navbar and footer
- [x] Created a Bootstrap 5 migration cheat sheet
- [x] Created layout adjustments CSS to maintain visual consistency
- [x] Fixed navbar layout and dropdown menu positioning
- [x] Fixed hamburger menu toggle functionality
- [x] Fixed navbar collapse z-index to appear above content
- [x] Created Bootstrap 5 version of Panel component (PanelBs5.vue)
- [x] Created Bootstrap 5 version of SpinnerSvg component (bs5/SpinnerSvg.vue)
- [x] Created Bootstrap 5 version of Modal component (bs5/Modal4.vue)
- [x] Created Bootstrap 5 version of AreYouSure component (bs5/AreYouSure.vue)
- [x] Updated RecurrenceOptions components to be compatible with Bootstrap 5
- [x] Updated DefaultChecklists components to be compatible with Bootstrap 5
- [x] Updated ListDefaultChecklists component to be compatible with Bootstrap 5
- [x] Updated EditDefaultChecklists component to be compatible with Bootstrap 5
- [x] Implemented CSS variables for consistent theming across components

### Next Steps
- [ ] Document all Bootstrap 4 components used in the application
- [ ] Identify custom Bootstrap overrides and variables
- [ ] Create a detailed component migration plan
- [ ] Migrate remaining page-specific components
- [ ] Update all form components to use Bootstrap 5 classes
- [ ] Test all migrated components for accessibility and responsiveness

### Component Migration Status

#### reusable components
- [x] Card components (Panel)
- [x] Modal dialogs
- [x] Spinner components
- [x] Confirmation dialogs (AreYouSure)

#### Page level components (including subcomponents)
- [x] DefaultChecklists components
- [x] RecurrenceOptions components
- [x] StudentLists components
- [x] SchoolYears components
- [x] MailTemplates components
- [x] Attendance options components
- [x] DomainSettings components
- [x] Profile components
- [x] Email contacts components
- [ ] Student access components
- [ ] Libraries components
- 

#### CK Editor upgrade:
- [x] MailTemplates.vue
- [ ] SendRequest.vue
- [ ] EmailStudentsWithAccess.vue
- [ ] EmailStudentWithAccess.vue
- [ ] StudentLog.vue
- [ ] CourseGroupGeneric.vue
- [ ] EmailContactBodyCreator.vue

