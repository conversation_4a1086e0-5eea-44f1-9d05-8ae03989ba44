<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translate('generic.studentaccess')) }}</h3>
        </template>
        <div class="row">
            <div class="col-xs-12 col-xl-3 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <student-access-codes-functions />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-xl-9 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <student-access-codes-list />
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { onMounted } from "vue";
import useStudentAccessCodes from "../composables/useStudentAccessCodes.js";
import StudentAccessCodesFunctions from "../components/Students/StudentAccessCodesFunctions.vue";
import StudentAccessCodesList from "../components/Students/StudentAccessCodesList.vue";
import Panel from "../components/Layout/Panel.vue";
import useLang from "../composables/useLang.js";
const { ucFirst, translate } = useLang();
const { busy, getStudents } = useStudentAccessCodes();

onMounted(() => {
    getStudents();
});

</script>

<style scoped>

</style>
